/// @ref ext_scalar_packing
/// @file glm/ext/scalar_packing.hpp
///
/// @see core (dependence)
///
/// @defgroup ext_scalar_packing GLM_EXT_scalar_packing
/// @ingroup ext
///
/// Include <glm/ext/scalar_packing.hpp> to use the features of this extension.
///
/// This extension provides a set of function to convert scalar values to packed
/// formats.

#pragma once

// Dependency:
#include "../detail/qualifier.hpp"

#if GLM_MESSAGES == GLM_ENABLE && !defined(GLM_EXT_INCLUDED)
#	pragma message("GLM: GLM_EXT_scalar_packing extension included")
#endif

namespace glm
{
	/// @addtogroup ext_scalar_packing
	/// @{


	/// @}
}// namespace glm

#include "scalar_packing.inl"
