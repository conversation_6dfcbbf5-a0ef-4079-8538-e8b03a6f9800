cmake_minimum_required(VERSION 3.10)
project(ReflectionEngine)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# إعدادات للويندوز
if(WIN32)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
    add_definitions(-DGLFW_DLL)
endif()

# البحث عن المكتبات
find_package(OpenGL REQUIRED)

# إذا كنت تستخدم vcpkg
if(DEFINED ENV{VCPKG_ROOT})
    set(CMAKE_TOOLCHAIN_FILE "$ENV{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake")
endif()

find_package(glfw3 REQUIRED)
find_package(GLEW REQUIRED)

# الملفات المصدر
set(SOURCES
    src/main.cpp
    src/ReflectionEngine.cpp
    src/ReflectionEngine.h
)

# إنشاء التنفيذي
add_executable(ReflectionEngine ${SOURCES})

# الربط مع المكتبات
target_link_libraries(ReflectionEngine 
    OpenGL::GL
    glfw
    GLEW::GLEW
)

# تضمين المجلدات
target_include_directories(ReflectionEngine PRIVATE
    include/
    ${GLFW3_INCLUDE_DIRS}
    ${GLEW_INCLUDE_DIRS}
)

# نسخ ملفات DLL المطلوبة (للويندوز)
if(WIN32)
    add_custom_command(TARGET ReflectionEngine POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${GLEW_LIBRARIES}/../bin/glew32.dll"
        $<TARGET_FILE_DIR:ReflectionEngine>
    )
endif()