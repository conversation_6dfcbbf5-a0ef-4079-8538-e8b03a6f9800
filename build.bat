@echo off
echo بناء محرك الانعكاسات...

g++ -std=c++17 -O2 -Wall -I src/ -I include/ -I stb/ -I ../Libraries/GLFW/include -I ../Libraries/GLEW/include -I ../Libraries/GLM/ ^
    src/main.cpp ^
    src/ReflectionEngine.cpp ^
    -o ReflectionEngine.exe ^
    -L ../Libraries/GLFW/lib-mingw-w64 -lglfw3 ^
    -L ../Libraries/GLEW/lib -lglew32 ^
    -lopengl32 -lgdi32

if %errorlevel% equ 0 (
    echo تم البناء بنجاح!
    copy ..\Libraries\GLEW\bin\glew32.dll .
    echo يمكنك الآن تشغيل ReflectionEngine.exe
) else (
    echo فشل البناء!
)